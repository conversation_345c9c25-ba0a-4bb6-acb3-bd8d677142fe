/* Seosona Header Styles - Based on Flatsome */

/* Reset and base styles for header */
.header * {
    box-sizing: border-box;
}

.seosona-header.header {
        background: transparent !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 999 !important;
    }
    .seosona-header .header-top {
        background: transparent !important;
    }

    .seosona-header .header-main {
        width: 1100px;
        margin: 0 auto !important;
        padding: 20px !important;
        height: auto !important;
        min-height: auto !important;
    }

    /* Force white background with highest priority */
    .seosona-header .header-main,
    .seosona-header.header .header-main,
    #header .header-main,
    .header .header-main,
    body .seosona-header .header-main,
    html body .seosona-header .header-main {
        background: white !important;
        background-color: white !important;
    }

    /* Override any gradient or color backgrounds */
    .seosona-header .header-main {
        background-image: none !important;
        background-color: white !important;
        color: #333333 !important;
    }

    /* Override specific color that might be causing #777777 */
    .seosona-header .header-main,
    .seosona-header .header-main * {
        background-color: white !important;
    }

    /* Remove any background colors from header wrapper */
    .seosona-header .header-wrapper,
    .seosona-header .header-bg-color,
    .seosona-header .header-bg-container {
        background: transparent !important;
        background-color: transparent !important;
    }

    /* Debug: Force white with extreme specificity */
    body.has-absolute-header .seosona-header .header-main,
    html body.has-absolute-header .seosona-header .header-main,
    html body .seosona-header.header .header-main {
        background: white !important;
        background-color: white !important;
        background-image: none !important;
    }

    /* Override any possible Flatsome colors */
    .header-main {
        background: white !important;
        background-color: white !important;
    }

    .seosona-header .header-bg-color {
        background-color: transparent !important;
    }

    .seosona-header #logo img {
        max-height: 50px !important;
        padding: 0 !important;
        filter: none !important;
    }

    .seosona-header .header-nav-main.nav>li>a {
        color: #333333 !important;
        font-weight: 500 !important;
        font-size: 16px !important;
        padding: 18px 0 !important;
        text-shadow: none !important;
    }

    .seosona-header .header-nav-main.nav>li>a:hover,
    .seosona-header .header-nav-main.nav>li.active>a {
        color: #007bff !important;
        opacity: 1 !important;
        transform: translateY(-1px) !important;
    }

    .seosona-header.transparent .header-wrapper {
        background-color: transparent !important;
    }

    .seosona-header.transparent .header-main {
        background: transparent !important;
    }

    /* Top bar responsive */
    @media (max-width: 768px) {
        .seosona-header .header-top {
            display: none !important;
        }
    }

    /* Mobile styles */
    @media (max-width: 549px) {
        .seosona-header .header-main {
            height: auto !important;
            padding: 15px 0 !important;
        }

        .seosona-header #logo img {
            max-height: 35px !important;
        }
    }

.seosona-header {
    position: relative;
    z-index: 999;
    width: 100%;
    background: #4A5FFF;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Sticky header styles */
.seosona-header.has-sticky {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 9999 !important;
    transition: all 0.3s ease;
}

.seosona-header.has-sticky.stuck {
    background: white !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important;
    transform: translateY(0) !important;
}

/* Hide top bar when sticky */
.seosona-header.has-sticky.stuck .header-top {
    display: none !important;
}

/* Add padding to body when header is sticky */
body.has-sticky-header {
    padding-top: 0;
}

body.has-sticky-header.header-stuck {
    padding-top: var(--header-height, 80px);
}

/* Smooth transition for sticky header */
.seosona-header.has-sticky.sticky-jump,
.seosona-header.sticky-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth transition for header elements when becoming sticky */
.seosona-header.sticky-transition .header-main,
.seosona-header.sticky-transition .header-nav>li>a,
.seosona-header.sticky-transition #logo img,
.seosona-header.sticky-transition .nav-icon a {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* When header becomes sticky, ensure it's visible */
.seosona-header.has-sticky.stuck {
    transform: translateY(0) !important;
    opacity: 1;
}

/* Adjust main header padding when sticky */
.seosona-header.has-sticky.stuck .header-main {
    padding: 15px 0;
    background: white !important;
}

/* Ensure logo size is appropriate when sticky */
.seosona-header.has-sticky.stuck #logo img {
    max-height: 45px;
    transition: max-height 0.3s ease;
}

/* Change navigation text color when sticky */
.seosona-header.has-sticky.stuck .header-nav>li>a {
    color: #333333 !important;
    text-shadow: none !important;
}

.seosona-header.has-sticky.stuck .header-nav>li>a:hover,
.seosona-header.has-sticky.stuck .header-nav>li.active>a {
    color: #007bff !important;
}

/* Change mobile menu button color when sticky */
.seosona-header.has-sticky.stuck .nav-icon a {
    color: #333333 !important;
    background: rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(0, 0, 0, 0.1) !important;
}

.seosona-header.has-sticky.stuck .nav-icon a:hover {
    background: rgba(0, 0, 0, 0.1) !important;
    color: #007bff !important;
}

/* Dropdown styles when header is sticky */
.seosona-header.has-sticky.stuck .sub-menu {
    border-top-color: #007bff;
}

.seosona-header.has-sticky.stuck .sub-menu a {
    color: #333333;
}

.seosona-header.has-sticky.stuck .sub-menu a:hover {
    color: #007bff;
}

/* Active navigation item underline when sticky */
.seosona-header.has-sticky.stuck .header-nav>li.active>a::after {
    background: #007bff !important;
}

.seosona-header.has-sticky.stuck .header-nav>li>a::after {
    background: #007bff;
}

.header-wrapper {
    position: relative;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Flex utilities */
.flex-row {
    display: flex;
    flex-wrap: wrap;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-left {
    justify-content: flex-start;
}

.flex-center {
    justify-content: center;
}

.flex-right {
    justify-content: flex-end;
}

.flex-grow {
    flex-grow: 1;
}

/* Top bar */
.header-top {
    background: transparent;
    color: #fff;
    padding: 12px 0;
    font-size: 14px;
    position: relative;
}

.header-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.header-top .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.header-top .flex-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-top .nav {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
}

.header-top .nav li {
    margin-right: 25px;
}

.header-top .nav li:last-child {
    margin-right: 0;
}

.header-top .nav a {
    color: #fff;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    font-size: 13px;
    font-weight: 400;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.header-top .nav a:hover {
    color: rgba(255, 255, 255, 0.8);
    transform: translateX(2px);
}

.header-top .nav i {
    margin-right: 8px;
    font-size: 12px;
    opacity: 0.9;
}

/* Social icons */
.social-icons {
    display: flex;
    gap: 8px;
}

.social-icons a {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.social-icons a:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Main header */
.seosona-header .header-main {
    background: white;
    padding: 40px;
    position: relative;
    box-shadow: none;
    border-radius: 8px;
    margin: 30;
}

/* Removed overlay for white background header */

.seosona-header .header-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Logo */
.seosona-header #logo {
    flex-shrink: 0;
    margin-right: 40px;
}

.seosona-header #logo img {
    max-height: 50px;
    width: auto;
    filter: none; /* Remove white filter */
    transition: all 0.3s ease;
}

.seosona-header #logo a {
    display: block;
    transition: transform 0.3s ease;
}

.seosona-header #logo a:hover {
    transform: scale(1.05);
}

/* Navigation */
.seosona-header .header-nav {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
}

.seosona-header .header-nav>li {
    position: relative;
    margin: 0 20px;
}

.seosona-header .header-nav>li>a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 18px 0;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    white-space: nowrap;
}

.seosona-header .header-nav>li>a:hover {
    color: #ffffff;
    opacity: 0.85;
    transform: translateY(-1px);
}

.seosona-header .header-nav>li.active>a {
    color: #ffffff;
    font-weight: 600;
    position: relative;
}

.seosona-header .header-nav>li.active>a::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: #ffffff;
    border-radius: 2px;
}

.seosona-header .header-nav i {
    margin-left: 6px;
    font-size: 11px;
    transition: transform 0.3s ease;
    opacity: 0.8;
}

.seosona-header .header-nav .has-dropdown:hover i {
    transform: rotate(180deg);
}

/* Dropdown */
.seosona-header .has-dropdown {
    position: relative;
}

.seosona-header .sub-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    background: #ffffff;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    min-width: 240px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    list-style: none;
    margin: 0;
    padding: 20px 0;
    border-top: 4px solid #4A5FFF;
    overflow: hidden;
}

.seosona-header .has-dropdown:hover .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(5px);
}

.seosona-header .sub-menu li {
    margin: 0;
}

.seosona-header .sub-menu a {
    display: block;
    padding: 14px 25px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    position: relative;
}

.seosona-header .sub-menu a:hover {
    background: linear-gradient(90deg, #f8f9ff 0%, #ffffff 100%);
    color: #4A5FFF;
    border-left-color: #4A5FFF;
    padding-left: 30px;
    transform: translateX(5px);
}

.seosona-header .sub-menu a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(90deg, rgba(74, 95, 255, 0.1) 0%, transparent 100%);
    transition: width 0.3s ease;
}

.seosona-header .sub-menu a:hover::before {
    width: 100%;
}

/* Search */
.header-search {
    position: relative;
}

.header-search>a {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.header-search>a:hover {
    background: #007cff;
    color: #fff;
}

.header-search .nav-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding: 20px;
    min-width: 300px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.header-search:hover .nav-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-field {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    outline: none;
    font-size: 14px;
}

.search-field:focus {
    border-color: #007cff;
}

.ux-search-submit {
    background: #007cff;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    margin-left: 10px;
    transition: background 0.3s ease;
}

.ux-search-submit:hover {
    background: #0056b3;
}

/* Mobile menu button */
.seosona-header .nav-icon a {
    width: 45px;
    height: 45px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.seosona-header .nav-icon a:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Responsive Design - Mobile First Approach */
.hide-for-medium {
    display: block;
}

.show-for-medium {
    display: none;
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    max-width: 85vw;
    height: 100vh;
    background: #ffffff;
    z-index: 9999;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #4A5FFF;
    color: white;
}

.mobile-menu-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.mobile-menu-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.mobile-menu-nav {
    list-style: none;
    margin: 0;
    padding: 20px 0;
}

.mobile-menu-nav li {
    margin: 0;
}

.mobile-menu-nav a {
    display: block;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    border-bottom: 1px solid #f5f5f5;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-menu-nav a:hover,
.mobile-menu-nav a.active {
    background: #f8f9ff;
    color: #4A5FFF;
    padding-left: 30px;
}

.mobile-menu-nav a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: #4A5FFF;
    transition: width 0.3s ease;
}

.mobile-menu-nav a:hover::before,
.mobile-menu-nav a.active::before {
    width: 4px;
}

.mobile-menu-nav .has-dropdown > a::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    float: right;
    transition: transform 0.3s ease;
}

.mobile-menu-nav .has-dropdown.open > a::after {
    transform: rotate(180deg);
}

.mobile-menu-nav .sub-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f8f9fa;
}

.mobile-menu-nav .has-dropdown.open .sub-menu {
    max-height: 500px;
}


.mobile-menu-nav .sub-menu a {
    padding-left: 40px;
    font-size: 14px;
    border-bottom: 1px solid #e9ecef;
}

.mobile-menu-nav .sub-menu a:hover {
    padding-left: 50px;
}

/* Mobile Login Button */
.mobile-login {
    padding: 20px;
    border-top: 1px solid #eee;
    margin-top: auto;
}

.mobile-login a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px;
    background: #e55a2b;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: background 0.3s ease;
}

.mobile-login a:hover {
    background: #d14d24;
}

.mobile-login .login-icon {
    width: 20px;
    height: 20px;
    background: transparent !important;
    background-color: transparent !important;
}

/* Tablet and Mobile Responsive Breakpoints */
@media (max-width: 1024px) {
    .seosona-header .header-nav>li {
        margin: 0 12px;
    }

    .seosona-header .header-nav>li>a {
        font-size: 15px;
        padding: 15px 0;
    }

    .seosona-header .header-inner {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .hide-for-medium {
        display: none;
    }

    .show-for-medium {
        display: block;
    }

    /* Hide top bar on mobile */
    .header-top {
        display: none;
    }

    .seosona-header .header-main {
        padding: 12px 0;
        width: 100%;
        margin: 0;
        border-radius: 0;
    }

    /* Mobile sticky header adjustments */
    .seosona-header.has-sticky.stuck .header-main {
        padding: 10px 0 !important;
    }

    .seosona-header.has-sticky.stuck #logo img {
        max-height: 35px !important;
    }

    .seosona-header .header-inner {
        padding: 0 15px;
        max-width: 100%;
    }

    .seosona-header #logo {
        margin-right: auto;
        flex-grow: 1;
    }

    .seosona-header #logo img {
        max-height: 40px;
    }

    .seosona-header .header-nav {
        display: none;
    }

    .seosona-header .mobile-nav {
        display: flex;
        align-items: center;
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .seosona-header .mobile-nav li {
        margin-left: 10px;
    }

    /* Mobile menu button enhanced - Blue icon */
    .seosona-header .nav-icon a {
        width: 44px;
        height: 44px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.15);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #007bff;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-size: 18px;
    }

    .seosona-header .nav-icon a:hover,
    .seosona-header .nav-icon a.active {
        background: rgba(255, 255, 255, 0.25);
        color: #0056b3;
        transform: scale(1.05);
    }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
    .seosona-header .header-inner {
        padding: 0 12px;
    }

    .seosona-header #logo img {
        max-height: 35px;
    }

    .seosona-header .header-main {
        padding: 10px 0;
    }

    .mobile-menu {
        width: 280px;
        max-width: 90vw;
    }

    .mobile-menu-nav a {
        padding: 12px 15px;
        font-size: 15px;
    }

    .mobile-menu-nav .sub-menu a {
        padding-left: 35px;
        font-size: 13px;
    }
}

/* Extra Small Mobile Devices */
@media (max-width: 360px) {
    .seosona-header .header-inner {
        padding: 0 10px;
    }

    .seosona-header #logo img {
        max-height: 32px;
    }

    .mobile-menu {
        width: 260px;
        max-width: 95vw;
    }

    .mobile-menu-header {
        padding: 15px;
    }

    .mobile-menu-nav a {
        padding: 10px 15px;
        font-size: 14px;
    }
}

/* Tablet Landscape */
@media (max-width: 1024px) and (min-width: 769px) {
    .header-top .nav li {
        margin-right: 15px;
    }

    .header-top .nav a {
        font-size: 12px;
    }

    .social-icons {
        gap: 6px;
    }

    .social-icons a {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .seosona-header .header-main {
        padding: 15px 0;
    }

    .seosona-header .header-inner {
        padding: 0 20px;
    }
}

/* Large Tablets and Small Laptops */
@media (max-width: 1200px) and (min-width: 1025px) {
    .seosona-header .header-nav>li {
        margin: 0 18px;
    }

    .seosona-header .header-nav>li>a {
        font-size: 15px;
    }

    .seosona-header .header-main {
        width: 95%;
        max-width: 1100px;
    }
}

/* Desktop and Large Screens */
@media (min-width: 1400px) {
    .seosona-header .header-main {
        width: 1200px;
        max-width: 90%;
    }

    .seosona-header .header-nav>li {
        margin: 0 25px;
    }

    .seosona-header .header-nav>li>a {
        font-size: 17px;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .seosona-header .header-nav>li>a {
        padding: 20px 0;
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    .seosona-header .nav-icon a {
        min-width: 44px;
        min-height: 44px;
    }

    .social-icons a {
        min-width: 44px;
        min-height: 44px;
    }

    .mobile-menu-nav a {
        min-height: 48px;
        display: flex;
        align-items: center;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .seosona-header #logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Orientation on Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .seosona-header .header-main {
        padding: 8px 0;
    }

    .seosona-header #logo img {
        max-height: 35px;
    }

    .mobile-menu {
        width: 320px;
    }
}

/* Print Styles */
@media print {
    .seosona-header .header-top,
    .seosona-header .mobile-nav,
    .mobile-menu,
    .mobile-menu-overlay {
        display: none !important;
    }

    .seosona-header .header-main {
        background: white !important;
        box-shadow: none !important;
        position: static !important;
    }

    .seosona-header .header-nav {
        display: none !important;
    }
}

/* Background container */
.header-bg-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.fill {
    width: 100%;
    height: 100%;
}

/* Transparent header styles */
.transparent .header-top {
    background: transparent;
}

.transparent .header-main {
    background: transparent;
}

.transparent .header-nav>li>a {
    color: #000;
}

.transparent .header-nav>li>a:hover {
    color: #007cff;
}

/* Utility classes */
.nav-dropdown-simple {
    list-style: none;
    margin: 0;
    padding: 0;
}

.relative {
    position: relative;
}

.mb-0 {
    margin-bottom: 0;
}

.ml-0 {
    margin-left: 0;
}

.text-left {
    text-align: left;
}

.z-top {
    z-index: 9999;
}

/* Seosona specific enhancements */
.seosona-header {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Add white background to header - Override inline styles */
.seosona-header .header-main {
    background: white !important;
    height: auto !important;
    padding: 10px !important;
    border-radius: 8px !important;
}

/* Override inline styles for header background */
.seosona-header .header-bg-color {
    background-color: transparent !important;
}

/* Override inline styles for logo */
.seosona-header #logo img {
    max-height: 45px !important;
    padding: 0 !important;
}

/* Override navigation colors from inline styles */
.seosona-header .header-nav-main.nav>li>a {
    text-shadow: none !important;
}

.seosona-header .header-nav-main.nav>li>a:hover,
.seosona-header .header-nav-main.nav>li.active>a,
.seosona-header .header-nav-main.nav>li.current>a,
.seosona-header .header-nav-main.nav>li>a.active,
.seosona-header .header-nav-main.nav>li>a.current {
    color: #007bff !important;
    opacity: 1;
}

/* Override transparent header styles */
.seosona-header.transparent .header-wrapper {
    background-color: transparent !important;
}

.seosona-header.transparent .header-main {
    background: white !important;
    height: auto !important;
    padding: 10px !important;
    border-radius: 8px !important;
}

/* Smooth hover effects for navigation */
.seosona-header .header-nav>li>a {
    position: relative;
    overflow: hidden;
}

.seosona-header .header-nav>li>a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: left 0.3s ease;
    z-index: -1;
}

.seosona-header .header-nav>li>a:hover::before {
    left: 0;
}

/* Logo hover effect */
.seosona-header #logo a {
    transition: transform 0.3s ease;
}

.seosona-header #logo a:hover {
    transform: scale(1.05);
}

/* Container improvements */
.seosona-header .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Ensure proper spacing */
.seosona-header .flex-center {
    justify-content: center;
}

.seosona-header .flex-grow {
    flex-grow: 1;
}

/* High priority overrides for inline styles */
.seosona-header.header {
    background: transparent !important;
}


.seosona-header .header-main {
    background: white !important;
    height: auto !important;
    min-height: auto !important;
    padding: 10px !important;
    border-radius: 8px !important;
}

/* Force navigation text to be white */
.seosona-header .nav>li>a {
    color: #ffffff !important;
}

.seosona-header .nav>li>a:hover {
    color: #ffffff !important;
    opacity: 0.8 !important;
}

/* Override any stuck/sticky styles */
.seosona-header.stuck .header-main {
    background: white !important;
    height: auto !important;
    padding: 10px !important;
    border-radius: 8px !important;
}

.seosona-header.stuck #logo img {
    max-height: 40px !important;
}

/* Override show-on-scroll styles */
.seosona-header.show-on-scroll .header-main {
    background: white !important;
    height: auto !important;
    padding: 10px !important;
    border-radius: 8px !important;
}

/* Touch Device Enhancements */
.touch-device .seosona-header .nav-icon a.touch-active,
.touch-device .social-icons a.touch-active,
.touch-device .login-btn.touch-active {
    transform: scale(0.95);
    opacity: 0.8;
}

/* Mobile Menu Icons */
.mobile-menu-nav a i {
    margin-right: 12px;
    width: 16px;
    text-align: center;
    opacity: 0.7;
}

.mobile-menu-nav a:hover i,
.mobile-menu-nav a.active i {
    opacity: 1;
}

/* Smooth Animations */
.seosona-header * {
    -webkit-tap-highlight-color: transparent;
}

.seosona-header .header-nav>li>a,
.mobile-menu-nav a,
.social-icons a,
.nav-icon a {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

/* Focus Styles for Accessibility */
.seosona-header a:focus,
.mobile-menu-nav a:focus,
.mobile-menu-close:focus {
    outline: 2px solid #4A5FFF;
    outline-offset: 2px;
}

/* Remove outline/border for nav-top-link when clicked/focused */
.nav-top-link:focus,
.nav-top-link:active,
.nav-top-link:focus-visible {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* Also remove any default browser focus styles for nav-top-link */
a.nav-top-link:focus,
a.nav-top-link:active,
a.nav-top-link:focus-visible {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Back to Top Button - Center the arrow icon */
#backToTop {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#backToTop i {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
}

/* Loading States */
.seosona-header.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Mobile Menu Content Spacing */
.mobile-menu-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.mobile-menu-nav {
    flex: 1;
}

/* Enhanced Mobile Menu Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.mobile-menu.active {
    animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .seosona-header *,
    .mobile-menu,
    .mobile-menu-overlay {
        transition: none !important;
        animation: none !important;
    }
}

/* Mobile overrides */
@media (max-width: 549px) {
    .seosona-header .header-main {
        height: auto !important;
        padding: 15px 0 !important;
    }

    .seosona-header #logo img {
        max-height: 35px !important;
    }
}

/* Login Button Container Styles */
.seosona-header .flex-col.hide-for-medium.flex-right {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    margin-left: auto !important;
    flex-shrink: 0 !important;
}

/* Enhanced Login Button Styles */
.seosona-header a.login-btn {
    color: #e55a2b !important;
    padding: 12px 20px !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    font-size: 16px !important;
    border: 2px solid #e55a2b !important;
    background: transparent !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
}

.seosona-header a.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: #e55a2b;
    transition: left 0.3s ease;
    z-index: -1;
}

.seosona-header a.login-btn:hover {
    color: white !important;
    box-shadow: 0 8px 25px rgba(229, 90, 43, 0.3) !important;
    transform: translateY(-2px) !important;
}

.seosona-header a.login-btn:hover::before {
    left: 0;
}

.seosona-header a.login-btn:active {
    transform: translateY(0) !important;
}

.seosona-header a.login-btn .login-icon {
    width: 20px !important;
    height: 20px !important;
    object-fit: contain !important;
    transition: transform 0.3s ease !important;
    background: transparent !important;
    background-color: transparent !important;
}

.seosona-header a.login-btn:hover .login-icon {
    transform: scale(1.1) !important;
}

/* Container Responsive Improvements */
.seosona-header .container {
    transition: max-width 0.3s ease;
}

/* Navigation Hover Improvements */
.seosona-header .header-nav>li>a {
    position: relative;
    overflow: hidden;
}

.seosona-header .header-nav>li>a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #ffffff;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.seosona-header .header-nav>li>a:hover::after,
.seosona-header .header-nav>li.active>a::after {
    width: 80%;
}

/* Improved Dropdown Animations */
.seosona-header .sub-menu {
    transform-origin: top center;
    animation-fill-mode: both;
}

.seosona-header .has-dropdown:hover .sub-menu {
    animation: dropdownFadeIn 0.3s ease;
}

@keyframes dropdownFadeIn {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(5px) scale(1);
    }
}

/* Mobile responsive for login button */
@media (max-width: 768px) {
    .seosona-header .flex-col.hide-for-medium.flex-right {
        display: none !important;
    }
}

/* Tablet responsive for login button */
@media (max-width: 1024px) and (min-width: 769px) {
    .seosona-header a.login-btn {
        padding: 10px 16px !important;
        font-size: 15px !important;
        gap: 6px !important;
    }

    .seosona-header a.login-btn .login-icon {
        width: 18px !important;
        height: 18px !important;
    }
}

/* Large Screen Optimizations */
@media (min-width: 1600px) {
    .seosona-header a.login-btn {
        padding: 14px 24px !important;
        font-size: 17px !important;
    }

    .seosona-header a.login-btn .login-icon {
        width: 22px !important;
        height: 22px !important;
    }
}

/* Performance Optimizations */
.seosona-header {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.mobile-menu {
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Accessibility Improvements */
.seosona-header [aria-expanded="true"] i {
    transform: rotate(180deg);
}

.seosona-header .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #4A5FFF;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
}

/* Browser Specific Fixes */
/* Safari iOS */
@supports (-webkit-touch-callout: none) {
    .mobile-menu {
        -webkit-overflow-scrolling: touch;
    }

    .seosona-header .header-nav>li>a {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }
}

/* Firefox */
@-moz-document url-prefix() {
    .seosona-header .header-nav>li>a {
        -moz-user-select: none;
    }
}

/* Edge/IE */
@supports (-ms-ime-align: auto) {
    .seosona-header .flex-row {
        display: -ms-flexbox;
        -ms-flex-wrap: wrap;
    }
}

/* Dark Mode Support (if needed in future) */
@media (prefers-color-scheme: dark) {
    .seosona-header.dark-mode .mobile-menu {
        background: #1a1a1a;
        color: #ffffff;
    }

    .seosona-header.dark-mode .mobile-menu-nav a {
        color: #ffffff;
        border-bottom-color: #333;
    }

    .seosona-header.dark-mode .mobile-menu-nav a:hover {
        background: #333;
    }
}

/* Final Responsive Tweaks */
@media (max-width: 320px) {
    .seosona-header .header-inner {
        padding: 0 8px;
    }

    .mobile-menu {
        width: 100%;
        max-width: 100vw;
    }
}

/* Landscape Phone Optimization */
@media (max-height: 500px) and (orientation: landscape) {
    .mobile-menu-header {
        padding: 10px 15px;
    }

    .mobile-menu-nav a {
        padding: 8px 15px;
    }
}
