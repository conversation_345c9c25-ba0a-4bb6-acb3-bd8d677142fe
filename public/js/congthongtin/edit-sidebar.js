/**
 * Edit Sidebar for Cổng Thông Tin Components
 * Handles content editing through sidebar interface
 */

class EditSidebar {
    constructor() {
        this.sidebar = null;
        this.currentComponent = null;
        this.apiBaseUrl = '/api/tuy-chinh-cong-thong-tin';
        this.isVisible = false;

        this.init();
    }

    init() {
        this.createSidebar();
        this.bindEvents();
    }

    /**
     * Create sidebar HTML structure
     */
    createSidebar() {
        const sidebarHTML = `
            <div id="edit-sidebar" class="edit-sidebar edit-sidebar-left">
                <div class="sidebar-header">
                    <h3><i class="fas fa-edit"></i> Chỉnh sửa nội dung</h3>
                    <button class="close-btn" id="closeSidebar">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="sidebar-content">
                    <div class="edit-form">
                        <div class="form-group">
                            <label for="componentTitle">
                                <i class="fas fa-heading"></i> Tiêu đề:
                            </label>
                            <input type="text" id="componentTitle" class="form-control" placeholder="Nhập tiêu đề...">
                        </div>

                        <div class="form-group">
                            <label for="componentDescription">
                                <i class="fas fa-align-left"></i> Mô tả:
                            </label>
                            <textarea id="componentDescription" class="form-control" rows="4" placeholder="Nhập mô tả..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="sidebar-footer">
                    <button type="button" class="btn btn-primary" id="saveChanges">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                    <button type="button" class="btn btn-secondary" id="cancelChanges">
                        <i class="fas fa-times"></i> Hủy
                    </button>
                </div>
            </div>
            <div id="sidebar-overlay" class="sidebar-overlay"></div>
        `;

        document.body.insertAdjacentHTML('beforeend', sidebarHTML);
        this.sidebar = document.getElementById('edit-sidebar');
    }

    /**
     * Show sidebar with component data
     */
    show(component) {
        this.currentComponent = component;
        this.isVisible = true;

        // Show sidebar and overlay
        this.sidebar.classList.add('active');
        document.getElementById('sidebar-overlay').classList.add('active');
        document.body.classList.add('sidebar-open');

        // Load component data
        this.loadComponentData(component);
    }

    /**
     * Hide sidebar
     */
    hide() {
        this.isVisible = false;
        this.currentComponent = null;

        // Hide sidebar and overlay
        this.sidebar.classList.remove('active');
        document.getElementById('sidebar-overlay').classList.remove('active');
        document.body.classList.remove('sidebar-open');

        // Clear form
        this.clearForm();
    }

    /**
     * Load component data into form
     */
    async loadComponentData(component) {
        // Get component type
        const componentType = this.getComponentType(component);

        // Update sidebar content based on component type
        this.updateSidebarContent(componentType);

        // Extract text content from component
        const textData = this.extractTextContent(component);

        // Fill form fields based on component type - with delay to ensure form is rendered
        setTimeout(async () => {
            await this.fillFormFields(componentType, textData);
            // Auto-resize textarea to match content
            this.autoResizeTextarea();
        }, 100);
    }

    /**
     * Get component type from element
     */
    getComponentType(component) {
        // Check class names to determine component type
        if (component.classList.contains('hero-right')) return 'hero-right';
        if (component.classList.contains('hero-left')) return 'hero-left';
        if (component.classList.contains('card-1')) return 'card-1';
        if (component.classList.contains('card-2')) return 'card-2';
        if (component.classList.contains('card-3')) return 'card-3';
        if (component.classList.contains('marketing-left')) return 'marketing-left';
        if (component.classList.contains('marketing-right')) return 'marketing-right';
        if (component.classList.contains('cot-1')) return 'footer-company';
        if (component.classList.contains('cot-2')) return 'footer-contact';

        // Check if it's header contact info
        if (component.closest('.header-top .nav-left')) return 'header-contact';

        // Check XPath for header contact info - /html/body/header/div/div[1]/div/div/div[1]/ul
        const xpath = this.getElementXPath(component);
        if (xpath.includes('/header/') && xpath.includes('/ul')) {
            return 'header-contact';
        }

        // Check XPath for section-1 components
        if (xpath.includes('/div[3]/div/div/div[1]') && component.closest('.section-1')) {
            return 'hero-left';
        }

        if (xpath.includes('/div[3]/div/div/div[2]') && component.closest('.section-1')) {
            return 'hero-right';
        }

        // Default to text-only
        return 'text-only';
    }

    /**
     * Get XPath of an element
     */
    getElementXPath(element) {
        if (element.id !== '') {
            return 'id("' + element.id + '")';
        }
        if (element === document.body) {
            return '/html/body';
        }

        let ix = 0;
        const siblings = element.parentNode.childNodes;
        for (let i = 0; i < siblings.length; i++) {
            const sibling = siblings[i];
            if (sibling === element) {
                return this.getElementXPath(element.parentNode) + '/' + element.tagName.toLowerCase() + '[' + (ix + 1) + ']';
            }
            if (sibling.nodeType === 1 && sibling.tagName === element.tagName) {
                ix++;
            }
        }
        return '';
    }

    /**
     * Update sidebar content based on component type
     */
    updateSidebarContent(componentType) {
        const sidebarContent = document.querySelector('.sidebar-content .edit-form');

        let formHTML = '';

        switch (componentType) {
            case 'hero-right':
            case 'marketing-left':
                formHTML = this.getImageOnlyForm();
                break;

            case 'card-1':
            case 'card-2':
            case 'card-3':
                formHTML = this.getImageTextForm();
                break;

            case 'footer-company':
                formHTML = this.getCompanyForm();
                break;

            case 'header-contact':
            case 'footer-contact':
                formHTML = this.getContactForm();
                break;

            case 'hero-left':
            case 'marketing-right':
            default:
                formHTML = this.getTextOnlyForm();
                break;
        }

        sidebarContent.innerHTML = formHTML;
    }

    /**
     * Get image-only form HTML
     */
    getImageOnlyForm() {
        return `
            <div class="form-group">
                <label for="componentImage">
                    <i class="fas fa-image"></i> Hình ảnh:
                </label>
                <div class="image-upload-container">
                    <img id="imagePreview" src="" alt="Preview" class="image-preview" style="display: none;">
                    <input type="file" id="componentImage" class="form-control" accept="image/*">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="changeImageBtn">
                        <i class="fas fa-camera"></i> Thay đổi hình ảnh
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Get image + text form HTML
     */
    getImageTextForm() {
        return `
            <div class="form-group">
                <label for="componentImage">
                    <i class="fas fa-image"></i> Hình ảnh:
                </label>
                <div class="image-upload-container">
                    <img id="imagePreview" src="" alt="Preview" class="image-preview" style="display: none;">
                    <input type="file" id="componentImage" class="form-control" accept="image/*">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="changeImageBtn">
                        <i class="fas fa-camera"></i> Thay đổi hình ảnh
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="componentTitle">
                    <i class="fas fa-heading"></i> Tiêu đề:
                </label>
                <input type="text" id="componentTitle" class="form-control" placeholder="Nhập tiêu đề...">
            </div>

            <div class="form-group">
                <label for="componentDescription">
                    <i class="fas fa-align-left"></i> Mô tả:
                </label>
                <textarea id="componentDescription" class="form-control auto-resize" rows="3" placeholder="Nhập mô tả..."></textarea>
            </div>
        `;
    }

    /**
     * Get company form HTML (for footer cot-1)
     */
    getCompanyForm() {
        return `
            <div class="form-group">
                <label for="componentLogo">
                    <i class="fas fa-image"></i> Logo công ty:
                </label>
                <div class="image-upload-container">
                    <img id="logoPreview" src="" alt="Logo Preview" class="image-preview" style="display: none;">
                    <input type="file" id="componentLogo" class="form-control" accept="image/*">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="changeLogoBtn">
                        <i class="fas fa-camera"></i> Thay đổi logo
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="componentCompanyName">
                    <i class="fas fa-building"></i> Tên công ty:
                </label>
                <input type="text" id="componentCompanyName" class="form-control" placeholder="Nhập tên công ty...">
            </div>

            <div class="form-group">
                <label for="componentSoftwareName">
                    <i class="fas fa-code"></i> Tên phần mềm:
                </label>
                <input type="text" id="componentSoftwareName" class="form-control" placeholder="Nhập tên phần mềm...">
            </div>

            <div class="form-group">
                <label for="componentFacebookUrl">
                    <i class="fab fa-facebook-f"></i> Facebook URL:
                </label>
                <input type="url" id="componentFacebookUrl" class="form-control" placeholder="https://facebook.com/...">
            </div>

            <div class="form-group">
                <label for="componentYoutubeUrl">
                    <i class="fab fa-youtube"></i> YouTube URL:
                </label>
                <input type="url" id="componentYoutubeUrl" class="form-control" placeholder="https://youtube.com/...">
            </div>
        `;
    }

    /**
     * Get contact form HTML
     */
    getContactForm() {
        return `
            <div class="form-group">
                <label for="componentTitle">
                    <i class="fas fa-heading"></i> Tiêu đề:
                </label>
                <input type="text" id="componentTitle" class="form-control" placeholder="Nhập tiêu đề...">
            </div>

            <div class="form-group">
                <label for="componentPhone">
                    <i class="fas fa-phone"></i> Số điện thoại:
                </label>
                <input type="text" id="componentPhone" class="form-control" placeholder="Nhập số điện thoại...">
            </div>

            <div class="form-group">
                <label for="componentEmail">
                    <i class="fas fa-envelope"></i> Email:
                </label>
                <input type="email" id="componentEmail" class="form-control" placeholder="Nhập địa chỉ email...">
            </div>

            <div class="form-group">
                <label for="componentAddress">
                    <i class="fas fa-map-marker-alt"></i> Địa chỉ:
                </label>
                <textarea id="componentAddress" class="form-control auto-resize" rows="3" placeholder="Nhập địa chỉ..."></textarea>
            </div>
        `;
    }

    /**
     * Get text-only form HTML
     */
    getTextOnlyForm() {
        return `
            <div class="form-group">
                <label for="componentTitle">
                    <i class="fas fa-heading"></i> Tiêu đề:
                </label>
                <input type="text" id="componentTitle" class="form-control" placeholder="Nhập tiêu đề...">
            </div>

            <div class="form-group">
                <label for="componentDescription">
                    <i class="fas fa-align-left"></i> Mô tả:
                </label>
                <textarea id="componentDescription" class="form-control auto-resize" rows="3" placeholder="Nhập mô tả..."></textarea>
            </div>
        `;
    }

    /**
     * Extract text content from component
     */
    extractTextContent(component) {
        const textData = {};

        // Extract title
        const titleElement = component.querySelector('h1, h2, h3, .hero-title, .service-title, .marketing-title');
        if (titleElement) {
            textData.title = titleElement.textContent.trim();
        }

        // Extract description
        const descElement = component.querySelector('p, .hero-description, .service-description, .marketing-description');
        if (descElement) {
            textData.description = descElement.textContent.trim();
        }

        // Extract image source - prioritize service-icon over wave-background
        let imgElement = component.querySelector('.service-icon img');
        if (!imgElement) {
            // Fallback to any img (for hero-right, marketing-left, etc.)
            imgElement = component.querySelector('img:not(.wave-svg)');
        }
        if (imgElement) {
            textData.imageSrc = imgElement.src;
        }

        // Extract contact info (for header/footer contact)
        const phoneElement = component.querySelector('a[href^="tel:"]');
        if (phoneElement) {
            textData.phone = phoneElement.textContent.replace(/[^\d\s\(\)\-\+]/g, '').trim();
        }

        const emailElement = component.querySelector('a[href^="mailto:"]');
        if (emailElement) {
            textData.email = emailElement.textContent.replace(/[^\w@\.\-]/g, '').trim();
        }

        const addressElement = component.querySelector('.fa-map-marker, .fa-map-marker-alt');
        if (addressElement && addressElement.parentElement) {
            textData.address = addressElement.parentElement.textContent.replace(/.*?(?=\s)/, '').trim();
        }

        // Extract company info (for footer cot-1)
        const logoElement = component.querySelector('.icon-box-img img');
        if (logoElement) {
            textData.logoSrc = logoElement.src;
        }

        const companyTextElement = component.querySelector('.icon-box-text p');
        if (companyTextElement) {
            const fullText = companyTextElement.textContent.trim();
            const parts = fullText.split(' - ');
            textData.companyName = parts[0] ? parts[0].trim() : '';
            textData.softwareName = parts[1] ? parts[1].trim() : '';
        }

        // Extract social links
        const facebookElement = component.querySelector('.social-icons .facebook');
        if (facebookElement) {
            textData.facebookUrl = facebookElement.href;
        }

        const youtubeElement = component.querySelector('.social-icons .youtube');
        if (youtubeElement) {
            textData.youtubeUrl = youtubeElement.href;
        }

        return textData;
    }

    /**
     * Fill form fields based on component type
     */
    async fillFormFields(componentType, textData) {
        console.log('🔄 Filling form fields for component type:', componentType);

        switch (componentType) {
            case 'hero-right':
            case 'marketing-left':
                await this.fillImageOnlyFields(textData);
                break;

            case 'card-1':
            case 'card-2':
            case 'card-3':
                await this.fillImageTextFields(textData);
                break;

            case 'footer-company':
                await this.fillCompanyFields(textData);
                break;

            case 'header-contact':
                await this.fillHeaderContactFields(textData);
                break;

            case 'footer-contact':
                await this.fillContactFields(textData);
                break;

            case 'hero-left':
            case 'marketing-right':
            default:
                await this.fillTextOnlyFields(textData);
                break;
        }

        console.log('✅ Form fields filled for component type:', componentType);
    }

    /**
     * Fill image-only fields
     */
    async fillImageOnlyFields(textData) {
        console.log('🔄 Loading image-only fields...');

        const imagePreview = document.getElementById('imagePreview');

        // Try to load from component's NoiDungText first
        const componentId = this.currentComponent?.dataset?.componentId;
        if (componentId) {
            try {
                const response = await fetch(`/api/tuy-chinh-cong-thong-tin/component/${componentId}`);
                const result = await response.json();

                if (!result.err && result.data && result.data.NoiDungText) {
                    const componentData = JSON.parse(result.data.NoiDungText);
                    console.log('✅ Component data loaded:', componentData);

                    if (imagePreview) {
                        const imageSrc = componentData.image_url || textData.imageSrc;
                        if (imageSrc) {
                            imagePreview.src = imageSrc;
                            imagePreview.style.display = 'block';
                        }
                    }
                    return;
                }
            } catch (error) {
                console.log('⚠️ Could not load component data, using extracted image:', error);
            }
        }

        // Fallback to extracted text data
        if (imagePreview && textData.imageSrc) {
            imagePreview.src = textData.imageSrc;
            imagePreview.style.display = 'block';
        }

        console.log('🖼️ Image-only fields filled:', {
            imageSrc: imagePreview?.src
        });
    }

    /**
     * Fill image + text fields
     */
    async fillImageTextFields(textData) {
        console.log('🔄 Loading image + text fields...');

        const titleField = document.getElementById('componentTitle');
        const descField = document.getElementById('componentDescription');
        const imagePreview = document.getElementById('imagePreview');

        // Try to load from component's NoiDungText first
        const componentId = this.currentComponent?.dataset?.componentId;
        if (componentId) {
            try {
                const response = await fetch(`/api/tuy-chinh-cong-thong-tin/component/${componentId}`);
                const result = await response.json();

                if (!result.err && result.data && result.data.NoiDungText) {
                    const componentData = JSON.parse(result.data.NoiDungText);
                    console.log('✅ Component data loaded:', componentData);

                    if (titleField) titleField.value = componentData.title || textData.title || '';
                    if (descField) descField.value = componentData.description || textData.description || '';

                    if (imagePreview) {
                        const imageSrc = componentData.image_url || textData.imageSrc;
                        if (imageSrc) {
                            imagePreview.src = imageSrc;
                            imagePreview.style.display = 'block';
                        }
                    }
                    return;
                }
            } catch (error) {
                console.log('⚠️ Could not load component data, using extracted text:', error);
            }
        }

        // Fallback to extracted text data
        if (titleField) titleField.value = textData.title || '';
        if (descField) descField.value = textData.description || '';

        if (imagePreview && textData.imageSrc) {
            imagePreview.src = textData.imageSrc;
            imagePreview.style.display = 'block';
        }

        console.log('🖼️ Image + text fields filled:', {
            title: titleField?.value,
            description: descField?.value,
            imageSrc: imagePreview?.src
        });
    }

    /**
     * Fill company fields
     */
    async fillCompanyFields(textData) {
        console.log('🔄 Loading company fields...');

        const logoPreview = document.getElementById('logoPreview');
        const companyNameField = document.getElementById('componentCompanyName');
        const softwareNameField = document.getElementById('componentSoftwareName');
        const facebookField = document.getElementById('componentFacebookUrl');
        const youtubeField = document.getElementById('componentYoutubeUrl');

        // Load current data from ThietLapWebsite
        try {
            const response = await fetch('/congthongtin/api/thietlapwebsite/load');
            const result = await response.json();

            let currentData = {};
            if (!result.err && result.Result && result.Result.length > 0) {
                currentData = result.Result[0];
                console.log('✅ Company data loaded:', currentData);
            }

            if (logoPreview && (currentData.LogoUrl || textData.logoSrc)) {
                logoPreview.src = currentData.LogoUrl || textData.logoSrc;
                logoPreview.style.display = 'block';
            }

            if (companyNameField) companyNameField.value = currentData.TenDonVi || textData.companyName || '';
            if (softwareNameField) softwareNameField.value = currentData.TenPhanMem || textData.softwareName || '';
            if (facebookField) facebookField.value = currentData.Facebook || textData.facebookUrl || '';
            if (youtubeField) youtubeField.value = currentData.Youtube || textData.youtubeUrl || '';

            console.log('🏢 Company fields filled:', {
                company: companyNameField?.value,
                software: softwareNameField?.value,
                facebook: facebookField?.value,
                youtube: youtubeField?.value
            });
        } catch (error) {
            console.error('❌ Error loading company data:', error);
            // Fallback to extracted text data
            if (logoPreview && textData.logoSrc) {
                logoPreview.src = textData.logoSrc;
                logoPreview.style.display = 'block';
            }

            if (companyNameField) companyNameField.value = textData.companyName || '';
            if (softwareNameField) softwareNameField.value = textData.softwareName || '';
            if (facebookField) facebookField.value = textData.facebookUrl || '';
            if (youtubeField) youtubeField.value = textData.youtubeUrl || '';
        }
    }

    /**
     * Fill header contact fields
     */
    async fillHeaderContactFields(textData) {
        console.log('🔄 Loading header contact data...');

        // Load current data from ThietLapWebsite
        try {
            const response = await fetch('/congthongtin/api/thietlapwebsite/load');
            const result = await response.json();

            console.log('📊 API Response:', result);

            let currentData = {};
            if (!result.err && result.data) {
                currentData = result.data;
                console.log('✅ Current data loaded:', currentData);
            }

            // Wait a bit more to ensure form fields are ready
            await new Promise(resolve => setTimeout(resolve, 50));

            const phoneField = document.getElementById('componentPhone');
            const emailField = document.getElementById('componentEmail');
            const addressField = document.getElementById('componentAddress');

            console.log('🔍 Form fields found:', {
                phone: !!phoneField,
                email: !!emailField,
                address: !!addressField
            });

            if (phoneField) {
                phoneField.value = currentData.SoDienThoai || '';
                console.log('📞 Phone set to:', phoneField.value);
            }
            if (emailField) {
                emailField.value = currentData.Email || '';
                console.log('📧 Email set to:', emailField.value);
            }
            if (addressField) {
                addressField.value = currentData.DiaChi || '';
                console.log('📍 Address set to:', addressField.value);
            }
        } catch (error) {
            console.error('❌ Error loading header contact data:', error);
            // Fallback to extracted text data
            const phoneField = document.getElementById('componentPhone');
            const emailField = document.getElementById('componentEmail');
            const addressField = document.getElementById('componentAddress');

            if (phoneField) phoneField.value = textData.phone || '';
            if (emailField) emailField.value = textData.email || '';
            if (addressField) addressField.value = textData.address || '';

            console.log('🔄 Fallback to extracted data:', textData);
        }
    }

    /**
     * Fill contact fields
     */
    async fillContactFields(textData) {
        console.log('🔄 Loading contact fields...');

        const phoneField = document.getElementById('componentPhone');
        const emailField = document.getElementById('componentEmail');
        const addressField = document.getElementById('componentAddress');
        const titleField = document.getElementById('componentTitle');

        // For footer contact, load from ThietLapWebsite like header contact
        try {
            const response = await fetch('/congthongtin/api/thietlapwebsite/load');
            const result = await response.json();

            if (!result.err && result.Result && result.Result.length > 0) {
                const websiteData = result.Result[0];
                console.log('✅ ThietLapWebsite data loaded for footer contact:', websiteData);

                if (phoneField) phoneField.value = websiteData.SoDienThoai || '';
                if (emailField) emailField.value = websiteData.Email || '';
                if (addressField) addressField.value = websiteData.DiaChi || '';
                if (titleField) titleField.value = 'Thông tin liên hệ'; // Fixed title
                return;
            }
        } catch (error) {
            console.error('Error loading ThietLapWebsite data:', error);
        }

        // Fallback to provided textData
        if (phoneField) phoneField.value = textData.phone || '';
        if (emailField) emailField.value = textData.email || '';
        if (addressField) addressField.value = textData.address || '';
        if (titleField) titleField.value = textData.title || 'Thông tin liên hệ';
    }

    /**
     * Fill text-only fields
     */
    async fillTextOnlyFields(textData) {
        console.log('🔄 Loading text-only fields...');

        const titleField = document.getElementById('componentTitle');
        const descField = document.getElementById('componentDescription');

        // Try to load from component's NoiDungText first
        const componentId = this.currentComponent?.dataset?.componentId;
        if (componentId) {
            try {
                const response = await fetch(`/api/tuy-chinh-cong-thong-tin/component/${componentId}`);
                const result = await response.json();

                if (!result.err && result.data && result.data.NoiDungText) {
                    const componentData = JSON.parse(result.data.NoiDungText);
                    console.log('✅ Component data loaded:', componentData);

                    if (titleField) titleField.value = componentData.title || textData.title || '';
                    if (descField) descField.value = componentData.description || textData.description || '';
                    return;
                }
            } catch (error) {
                console.log('⚠️ Could not load component data, using extracted text:', error);
            }
        }

        // Fallback to extracted text data
        if (titleField) titleField.value = textData.title || '';
        if (descField) descField.value = textData.description || '';

        console.log('📝 Text fields filled:', {
            title: titleField?.value,
            description: descField?.value
        });
    }

    /**
     * Auto-resize textarea to match content
     */
    autoResizeTextarea() {
        const textareas = document.querySelectorAll('.auto-resize');
        textareas.forEach(textarea => {
            // Reset height to auto to get the correct scrollHeight
            textarea.style.height = 'auto';
            // Set height to scrollHeight to fit content
            textarea.style.height = Math.max(textarea.scrollHeight, 60) + 'px';

            // Add input event listener for dynamic resizing
            textarea.addEventListener('input', function () {
                this.style.height = 'auto';
                this.style.height = Math.max(this.scrollHeight, 60) + 'px';
            });
        });
    }

    /**
     * Get component name
     */
    getComponentName(component) {
        if (component.classList.contains('hero-left')) return 'Hero Left';
        if (component.classList.contains('hero-right')) return 'Hero Right';
        if (component.classList.contains('card-1')) return 'Card 1';
        if (component.classList.contains('card-2')) return 'Card 2';
        if (component.classList.contains('card-3')) return 'Card 3';
        if (component.classList.contains('marketing-left')) return 'Marketing Left';
        if (component.classList.contains('marketing-right')) return 'Marketing Right';
        if (component.classList.contains('cot-1')) return 'Cột 1';
        if (component.classList.contains('cot-2')) return 'Cột 2';
        if (component.classList.contains('cot-3')) return 'Cột 3';
        return 'Unknown Component';
    }

    /**
     * Get section name
     */
    getSectionName(component) {
        const section = component.closest('.section-1, .section-2, .section-3, .footer-wrapper');
        if (!section) return 'Unknown Section';

        if (section.classList.contains('section-1')) return 'Section 1 - Hero';
        if (section.classList.contains('section-2')) return 'Section 2 - Services';
        if (section.classList.contains('section-3')) return 'Section 3 - Marketing';
        if (section.classList.contains('footer-wrapper')) return 'Footer';
        return 'Unknown Section';
    }

    /**
     * Save changes to component
     */
    async saveChanges() {
        if (!this.currentComponent) return;

        const componentType = this.getComponentType(this.currentComponent);
        const formData = this.collectFormData(componentType);

        // Update component content
        this.updateComponentContent(this.currentComponent, formData, componentType);

        // Save to database
        const saveResult = await this.saveToDatabase(formData, componentType);

        // Hide sidebar
        this.hide();

        // Show success message only if save was successful
        if (saveResult !== false) {
            this.showNotification('Lưu thay đổi thành công!', 'success');
        }
    }

    /**
     * Collect form data based on component type
     */
    collectFormData(componentType) {
        const formData = {};

        switch (componentType) {
            case 'hero-right':
            case 'marketing-left':
                const imageField = document.getElementById('componentImage');
                if (imageField && imageField.files[0]) {
                    formData.image = imageField.files[0];
                }
                break;

            case 'card-1':
            case 'card-2':
            case 'card-3':
                const titleField = document.getElementById('componentTitle');
                const descField = document.getElementById('componentDescription');
                const cardImageField = document.getElementById('componentImage');

                if (titleField) formData.title = titleField.value;
                if (descField) formData.description = descField.value;
                if (cardImageField && cardImageField.files[0]) {
                    formData.image = cardImageField.files[0];
                }
                break;

            case 'footer-company':
                const logoField = document.getElementById('componentLogo');
                const companyNameField = document.getElementById('componentCompanyName');
                const softwareNameField = document.getElementById('componentSoftwareName');
                const facebookField = document.getElementById('componentFacebookUrl');
                const youtubeField = document.getElementById('componentYoutubeUrl');

                if (logoField && logoField.files[0]) formData.logo = logoField.files[0];
                if (companyNameField) formData.companyName = companyNameField.value;
                if (softwareNameField) formData.softwareName = softwareNameField.value;
                if (facebookField) formData.facebookUrl = facebookField.value;
                if (youtubeField) formData.youtubeUrl = youtubeField.value;
                break;

            case 'header-contact':
                const headerPhoneField = document.getElementById('componentPhone');
                const headerEmailField = document.getElementById('componentEmail');
                const headerAddressField = document.getElementById('componentAddress');

                if (headerPhoneField) formData.phone = headerPhoneField.value;
                if (headerEmailField) formData.email = headerEmailField.value;
                if (headerAddressField) formData.address = headerAddressField.value;
                break;

            case 'footer-contact':
                const footerTitleField = document.getElementById('componentTitle');
                const phoneField = document.getElementById('componentPhone');
                const emailField = document.getElementById('componentEmail');
                const addressField = document.getElementById('componentAddress');

                if (footerTitleField) formData.title = footerTitleField.value;
                if (phoneField) formData.phone = phoneField.value;
                if (emailField) formData.email = emailField.value;
                if (addressField) formData.address = addressField.value;
                break;

            case 'hero-left':
            case 'marketing-right':
            default:
                const textTitleField = document.getElementById('componentTitle');
                const textDescField = document.getElementById('componentDescription');

                if (textTitleField) formData.title = textTitleField.value;
                if (textDescField) formData.description = textDescField.value;
                break;
        }

        return formData;
    }

    /**
     * Update component content in DOM
     */
    updateComponentContent(component, data, componentType) {
        switch (componentType) {
            case 'hero-right':
            case 'marketing-left':
                this.updateImageComponent(component, data);
                break;

            case 'card-1':
            case 'card-2':
            case 'card-3':
                this.updateImageTextComponent(component, data);
                break;

            case 'footer-company':
                this.updateCompanyComponent(component, data);
                break;

            case 'header-contact':
                this.updateHeaderContact(component, data);
                break;

            case 'footer-contact':
                this.updateFooterContact(component, data);
                break;

            case 'hero-left':
            case 'marketing-right':
            default:
                this.updateTextComponent(component, data);
                break;
        }
    }

    /**
     * Update image-only component
     */
    updateImageComponent(component, data) {
        if (data.image) {
            const imgElement = component.querySelector('img');
            if (imgElement) {
                // Create object URL for preview
                const imageUrl = URL.createObjectURL(data.image);
                imgElement.src = imageUrl;
            }
        }
    }

    /**
     * Update image + text component
     */
    updateImageTextComponent(component, data) {
        // Update all titles (for flip cards with front and back)
        const titleElements = component.querySelectorAll('h1, h2, h3, .hero-title, .service-title, .marketing-title');
        if (titleElements.length > 0 && data.title) {
            titleElements.forEach(titleElement => {
                titleElement.textContent = data.title;
            });
        }

        // Update all descriptions (for flip cards with front and back)
        const descElements = component.querySelectorAll('p, .hero-description, .service-description, .marketing-description');
        if (descElements.length > 0 && data.description) {
            descElements.forEach(descElement => {
                descElement.textContent = data.description;
            });
        }

        // Update all images (for flip cards with front and back)
        if (data.imageUrl) {
            const imgElements = component.querySelectorAll('img');
            if (imgElements.length > 0) {
                imgElements.forEach(imgElement => {
                    imgElement.src = data.imageUrl;
                });
            }
        } else if (data.image && data.image instanceof File) {
            // Fallback for preview during editing
            const imgElements = component.querySelectorAll('img');
            if (imgElements.length > 0) {
                const imageUrl = URL.createObjectURL(data.image);
                imgElements.forEach(imgElement => {
                    imgElement.src = imageUrl;
                });
            }
        }
    }

    /**
     * Update text-only component
     */
    updateTextComponent(component, data) {
        // Update all titles (for consistency with flip cards)
        const titleElements = component.querySelectorAll('h1, h2, h3, .hero-title, .service-title, .marketing-title');
        if (titleElements.length > 0 && data.title) {
            titleElements.forEach(titleElement => {
                titleElement.textContent = data.title;
            });
        }

        // Update all descriptions (for consistency with flip cards)
        const descElements = component.querySelectorAll('p, .hero-description, .service-description, .marketing-description');
        if (descElements.length > 0 && data.description) {
            descElements.forEach(descElement => {
                descElement.textContent = data.description;
            });
        }
    }

    /**
     * Update company component (footer cot-1)
     */
    updateCompanyComponent(component, data) {
        console.log('🔄 Updating company component with data:', data);

        // Update logo
        if (data.logoUrl) {
            const logoElement = component.querySelector('.icon-box-img img');
            if (logoElement) {
                console.log('📷 Updating logo:', data.logoUrl);
                logoElement.src = data.logoUrl;
                logoElement.alt = data.companyName || logoElement.alt;
            }
        } else if (data.logo && data.logo instanceof File) {
            // Fallback for preview during editing
            const logoElement = component.querySelector('.icon-box-img img');
            if (logoElement) {
                const imageUrl = URL.createObjectURL(data.logo);
                logoElement.src = imageUrl;
            }
        }

        // Update company text
        const textElement = component.querySelector('.icon-box-text p');
        if (textElement && (data.companyName || data.softwareName)) {
            const companyName = data.companyName || 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm';
            const softwareName = data.softwareName || 'Hệ thống quản lý văn bằng chứng chỉ';
            const newText = `${companyName} - ${softwareName}`;
            console.log('📝 Updating company text:', newText);
            textElement.textContent = newText;
        }

        // Update social links
        if (data.facebookUrl) {
            const facebookElement = component.querySelector('.social-icons .facebook');
            if (facebookElement) {
                console.log('🔗 Updating Facebook URL:', data.facebookUrl);
                facebookElement.href = data.facebookUrl;
            }
        }

        if (data.youtubeUrl) {
            const youtubeElement = component.querySelector('.social-icons .youtube');
            if (youtubeElement) {
                console.log('🔗 Updating YouTube URL:', data.youtubeUrl);
                youtubeElement.href = data.youtubeUrl;
            }
        }

        console.log('✅ Company component updated successfully');
    }

    /**
     * Update header contact info
     */
    updateHeaderContact(_component, _data) {
        // This will be handled by the header edit functionality
        // For now, just show a message
        this.showNotification('Header contact sẽ được cập nhật qua chức năng header edit', 'info');
    }

    /**
     * Update footer contact info
     */
    updateFooterContact(component, data) {
        // Update title
        if (data.title) {
            const titleElement = component.querySelector('h3');
            if (titleElement) {
                titleElement.textContent = data.title;
            }
        }

        // Update phone
        if (data.phone) {
            const phoneElement = component.querySelector('a[href^="tel:"], .fa-phone');
            if (phoneElement) {
                if (phoneElement.tagName === 'A') {
                    phoneElement.href = `tel:${data.phone.replace(/\D/g, '')}`;
                    phoneElement.innerHTML = `<i class="fa fa-phone"></i> Hotline: ${data.phone}`;
                } else if (phoneElement.parentElement) {
                    phoneElement.parentElement.innerHTML = `<i class="fa fa-phone"></i> Hotline: ${data.phone}`;
                }
            }
        }

        // Update email
        if (data.email) {
            const emailElement = component.querySelector('a[href^="mailto:"], .fa-envelope');
            if (emailElement) {
                if (emailElement.tagName === 'A') {
                    emailElement.href = `mailto:${data.email}`;
                    emailElement.innerHTML = `<i class="fa fa-envelope"></i> ${data.email}`;
                } else if (emailElement.parentElement) {
                    emailElement.parentElement.innerHTML = `<i class="fa fa-envelope"></i> ${data.email}`;
                }
            }
        }

        // Update address
        if (data.address) {
            const addressElement = component.querySelector('.fa-map-marker');
            if (addressElement && addressElement.parentElement) {
                addressElement.parentElement.innerHTML = `<i class="fa fa-map-marker"></i> ${data.address}`;
            }
        }
    }

    /**
     * Save to database
     */
    async saveToDatabase(data, componentType) {
        // Handle company data differently
        if (componentType === 'footer-company') {
            return await this.saveCompanyData(data);
        }

        // Handle header contact info
        if (componentType === 'header-contact') {
            return await this.saveHeaderContactData(data);
        }

        // Handle footer contact info - save to ThietLapWebsite like header
        if (componentType === 'footer-contact') {
            return await this.saveHeaderContactData(data);
        }

        // Handle image upload first if there's an image
        if (data.image && data.image instanceof File) {
            console.log('🖼️ Uploading image first...');
            const imageUrl = await this.uploadImage(data.image, componentType);
            if (imageUrl) {
                data.imageUrl = imageUrl;
                // Remove the File object, keep only the URL
                delete data.image;
            } else {
                this.showNotification('Lỗi khi tải lên hình ảnh', 'error');
                return false;
            }
        }

        // For TuyChinhCongThongTin components
        const componentId = this.currentComponent.dataset.componentId;
        if (!componentId) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/update-noi-dung`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    componentId: componentId,
                    noiDungText: JSON.stringify(data)
                })
            });

            const result = await response.json();

            if (result.err) {
                console.error('Error saving component:', result.msg);
                this.showNotification('Có lỗi xảy ra khi lưu', 'error');
                return false;
            }
            return true;
        } catch (error) {
            console.error('Error saving component:', error);
            this.showNotification('Có lỗi xảy ra khi lưu', 'error');
            return false;
        }
    }

    /**
     * Upload image using existing API
     */
    async uploadImage(imageFile, componentType) {
        try {
            const formData = new FormData();
            formData.append('file', imageFile);

            // Set folder based on component type
            let thuMucLuu = 'components';
            switch (componentType) {
                case 'hero-left':
                case 'hero-right':
                    thuMucLuu = 'hero';
                    break;
                case 'card-1':
                case 'card-2':
                case 'card-3':
                    thuMucLuu = 'cards';
                    break;
                case 'marketing-left':
                case 'marketing-right':
                    thuMucLuu = 'marketing';
                    break;
                case 'footer-company':
                    thuMucLuu = 'company';
                    break;
                default:
                    thuMucLuu = 'components';
            }

            formData.append('thuMucLuu', thuMucLuu);

            console.log('🔄 Uploading image to:', `/api/congthongtin/dungchung/files/upload-image`);

            const response = await fetch('/api/congthongtin/dungchung/files/upload-image', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            });

            const result = await response.json();

            if (result.success && result.url) {
                console.log('✅ Image uploaded successfully:', result.url);
                return result.url;
            } else {
                console.error('❌ Image upload failed:', result);
                return null;
            }
        } catch (error) {
            console.error('❌ Error uploading image:', error);
            return null;
        }
    }

    /**
     * Save header contact data to ThietLapWebsite
     */
    async saveHeaderContactData(data) {
        try {
            // Load current data first to preserve other fields
            const loadResponse = await fetch('/congthongtin/api/thietlapwebsite/load');
            const loadResult = await loadResponse.json();

            let currentData = {};
            if (!loadResult.err && loadResult.data) {
                currentData = loadResult.data;
            }

            // Prepare form data - ensure TenDonVi has a value
            const formData = new URLSearchParams();
            formData.append('TenDonVi', currentData.TenDonVi || 'CÔNG TY TNHH PHÁT TRIỂN PHẦN MÊM NHẤT TÂM - NTSOFT');
            formData.append('DiaChi', data.address || currentData.DiaChi || '');
            formData.append('SoDienThoai', data.phone || currentData.SoDienThoai || '');
            formData.append('Email', data.email || currentData.Email || '');
            formData.append('Fax', currentData.Fax || '');
            formData.append('TenPhanMem', currentData.TenPhanMem || '');
            formData.append('TenPhienBan', currentData.TenPhienBan || '');
            formData.append('Website', currentData.Website || '');
            formData.append('Facebook', currentData.Facebook || '');
            formData.append('Youtube', currentData.Youtube || '');

            const response = await fetch('/congthongtin/api/thietlapwebsite/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            });

            const result = await response.json();

            if (result.err) {
                console.error('Error saving header contact data:', result.msg);
                this.showNotification('Lỗi: ' + result.msg, 'error');
                return false;
            } else {
                // Don't show notification here, let saveChanges handle it
                // Refresh page after 2 seconds to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                return true;
            }
        } catch (error) {
            console.error('Error saving header contact data:', error);
            this.showNotification('Có lỗi xảy ra khi lưu thông tin liên hệ', 'error');
            return false;
        }
    }

    /**
     * Save company data to ThietLapWebsite
     */
    async saveCompanyData(data) {
        try {
            // Validate required fields
            if (!data.companyName || data.companyName.trim() === '') {
                this.showNotification('Tên công ty là bắt buộc', 'error');
                return;
            }
            if (!data.softwareName || data.softwareName.trim() === '') {
                this.showNotification('Tên phần mềm là bắt buộc', 'error');
                return;
            }

            // Handle logo upload first if there's a logo
            if (data.logo && data.logo instanceof File) {
                console.log('🖼️ Uploading logo first...');
                const logoUrl = await this.uploadImage(data.logo, 'footer-company');
                if (logoUrl) {
                    data.logoUrl = logoUrl;
                } else {
                    this.showNotification('Lỗi khi tải lên logo', 'error');
                    return false;
                }
            }

            // Prepare request data
            const requestData = {
                companyName: data.companyName.trim(),
                softwareName: data.softwareName.trim(),
                facebookUrl: data.facebookUrl || '',
                youtubeUrl: data.youtubeUrl || '',
                logoUrl: data.logoUrl || ''
            };

            console.log('🔄 Sending company data:', requestData);

            const response = await fetch('/congthongtin/api/company/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.err) {
                console.error('❌ Error saving company data:', result.msg);
                this.showNotification('Lỗi: ' + result.msg, 'error');
                return false;
            } else {
                console.log('✅ Company data saved successfully:', result.data);
                console.log('🔄 ThietLapWebsite should now contain:', {
                    TenDonVi: requestData.companyName,
                    TenPhanMem: requestData.softwareName,
                    Facebook: requestData.facebookUrl,
                    Youtube: requestData.youtubeUrl,
                    LogoUrl: requestData.logoUrl
                });
                return true;
            }
        } catch (error) {
            console.error('Error saving company data:', error);
            this.showNotification('Có lỗi xảy ra khi lưu dữ liệu', 'error');
            return false;
        }
    }

    /**
     * Clear form
     */
    clearForm() {
        // Clear text fields
        const titleField = document.getElementById('componentTitle');
        const descField = document.getElementById('componentDescription');
        const phoneField = document.getElementById('componentPhone');
        const emailField = document.getElementById('componentEmail');
        const addressField = document.getElementById('componentAddress');
        const imageField = document.getElementById('componentImage');
        const imagePreview = document.getElementById('imagePreview');

        // Clear company fields
        const logoField = document.getElementById('componentLogo');
        const logoPreview = document.getElementById('logoPreview');
        const companyNameField = document.getElementById('componentCompanyName');
        const softwareNameField = document.getElementById('componentSoftwareName');
        const facebookField = document.getElementById('componentFacebookUrl');
        const youtubeField = document.getElementById('componentYoutubeUrl');

        if (titleField) titleField.value = '';
        if (descField) descField.value = '';
        if (phoneField) phoneField.value = '';
        if (emailField) emailField.value = '';
        if (addressField) addressField.value = '';
        if (imageField) imageField.value = '';
        if (imagePreview) {
            imagePreview.src = '';
            imagePreview.style.display = 'none';
        }

        if (logoField) logoField.value = '';
        if (logoPreview) {
            logoPreview.src = '';
            logoPreview.style.display = 'none';
        }
        if (companyNameField) companyNameField.value = '';
        if (softwareNameField) softwareNameField.value = '';
        if (facebookField) facebookField.value = '';
        if (youtubeField) youtubeField.value = '';
    }

    /**
     * Show notification using existing NTS system with debounce
     */
    showNotification(message, type = 'info') {
        // Clear any existing timeout for this message
        const messageKey = `${type}_${message}`;
        if (this.notificationTimeouts && this.notificationTimeouts[messageKey]) {
            clearTimeout(this.notificationTimeouts[messageKey]);
        }

        // Initialize timeouts object if not exists
        if (!this.notificationTimeouts) {
            this.notificationTimeouts = {};
        }

        // Set timeout to prevent rapid duplicate notifications
        this.notificationTimeouts[messageKey] = setTimeout(() => {
            // Use existing NTS notification system
            switch (type) {
                case 'success':
                    NTS.thanhcong(message);
                    break;
                case 'error':
                    NTS.loi(message);
                    break;
                case 'warning':
                    NTS.canhbao(message);
                    break;
                case 'info':
                default:
                    NTS.thongbao({ type: 'info', message: message });
                    break;
            }

            // Clean up timeout
            delete this.notificationTimeouts[messageKey];
        }, 100); // 100ms debounce
    }

    /**
     * Bind events
     */
    bindEvents() {
        // Close sidebar
        document.addEventListener('click', (e) => {
            if (e.target.id === 'closeSidebar' || e.target.id === 'sidebar-overlay') {
                this.hide();
            }
        });

        // Save changes
        document.addEventListener('click', (e) => {
            if (e.target.id === 'saveChanges') {
                this.saveChanges();
            }
        });

        // Cancel changes
        document.addEventListener('click', (e) => {
            if (e.target.id === 'cancelChanges') {
                this.hide();
            }
        });

        // Component click to edit
        document.addEventListener('click', (e) => {
            if (document.body.classList.contains('edit-mode')) {
                const component = e.target.closest('.editable-component');
                if (component) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.show(component);
                }
            }
        });

        // Image upload button click
        document.addEventListener('click', (e) => {
            if (e.target.id === 'changeImageBtn') {
                const fileInput = document.getElementById('componentImage');
                if (fileInput) {
                    fileInput.click();
                }
            }

            // Logo upload button click
            if (e.target.id === 'changeLogoBtn') {
                const fileInput = document.getElementById('componentLogo');
                if (fileInput) {
                    fileInput.click();
                }
            }
        });

        // Image file change
        document.addEventListener('change', (e) => {
            if (e.target.id === 'componentImage') {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (event) {
                        const imagePreview = document.getElementById('imagePreview');
                        if (imagePreview) {
                            imagePreview.src = event.target.result;
                            imagePreview.style.display = 'block';
                        }
                    };
                    reader.readAsDataURL(file);
                }
            }

            // Logo file change
            if (e.target.id === 'componentLogo') {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (event) {
                        const logoPreview = document.getElementById('logoPreview');
                        if (logoPreview) {
                            logoPreview.src = event.target.result;
                            logoPreview.style.display = 'block';
                        }
                    };
                    reader.readAsDataURL(file);
                }
            }
        });

        // ESC key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isVisible) {
                this.hide();
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.editSidebar = new EditSidebar();
});
