<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Test Footer Cot-1 Component</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ asset('css/congthongtin/style.css') }}" rel="stylesheet">
    <link href="{{ asset('css/congthongtin/edit-mode.css') }}" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .test-section { border: 2px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .test-section h3 { margin-top: 0; color: #333; }
        .footer-test { background: #2c3e50; color: white; padding: 20px; }
        .icon-box { display: flex; align-items: center; gap: 15px; }
        .icon-box-img img { max-width: 150px; height: auto; }
        .social-icons { margin-top: 10px; }
        .social-icons a { color: white; margin-right: 10px; font-size: 18px; text-decoration: none; }
        .social-icons a:hover { color: #3498db; }
        .edit-controls { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
    </style>
</head>
<body>
    <h1>🧪 Test Footer Cot-1 Component</h1>
    
    <div class="debug-info">
        <h3>📊 Debug Information</h3>
        <p><strong>ThietLapWebsite Data:</strong></p>
        <ul>
            <li>TenDonVi: {{ $thietLapWebsite->TenDonVi ?? 'NULL' }}</li>
            <li>TenPhanMem: {{ $thietLapWebsite->TenPhanMem ?? 'NULL' }}</li>
            <li>Facebook: {{ $thietLapWebsite->Facebook ?? 'NULL' }}</li>
            <li>Youtube: {{ $thietLapWebsite->Youtube ?? 'NULL' }}</li>
            <li>LogoUrl: {{ $thietLapWebsite->LogoUrl ?? 'NULL' }}</li>
            <li>NgayCapNhat: {{ $thietLapWebsite->NgayCapNhat ?? 'NULL' }}</li>
        </ul>
        
        <p><strong>Footer Components:</strong></p>
        @foreach($footerComponents as $component)
            <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px;">
                <strong>{{ $component->TenComponent }}</strong> - 
                NoiDungText: {{ $component->NoiDungText ? 'HAS DATA' : 'NULL' }}
                @if($component->NoiDungText)
                    <br><small>{{ Str::limit($component->NoiDungText, 100) }}</small>
                @endif
            </div>
        @endforeach
    </div>

    <div class="edit-controls">
        <button class="btn btn-primary" onclick="toggleEditMode()">🔧 Toggle Edit Mode</button>
        <button class="btn btn-success" onclick="testSave()">💾 Test Save</button>
    </div>

    <div class="test-section">
        <h3>🎯 Footer Component Test</h3>
        <div class="footer-test">
            @foreach($footerComponents as $component)
                @if($component->TenComponent == 'cot-1')
                    <div class="editable-component footer-company" 
                         data-component-type="footer-company"
                         data-component-id="{{ $component->_id }}">
                        <div class="icon-box">
                            <div class="icon-box-img">
                                <img src="{{ $thietLapWebsite->LogoUrl ?? asset('assets/congthongtin/Seosona-Logo-1-300x116.png') }}"
                                    alt="{{ $thietLapWebsite->TenDonVi ?? 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm' }}"
                                    loading="lazy">
                            </div>
                            <div class="icon-box-text">
                                <p>{{ $thietLapWebsite->TenDonVi ?? 'Công ty TNHH Phát Triển Phần Mềm Nhất Tâm' }}
                                    -
                                    {{ $thietLapWebsite->TenPhanMem ?? 'Hệ thống quản lý văn bằng chứng chỉ' }}
                                </p>
                            </div>
                        </div>
                        <div class="social-icons" role="navigation" aria-label="Social Media Links">
                            @if($thietLapWebsite->Facebook && $thietLapWebsite->Facebook !== '#')
                                <a href="{{ $thietLapWebsite->Facebook }}" class="icon facebook"
                                    target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                                    <i class="fab fa-facebook-f" aria-hidden="true"></i>
                                </a>
                            @endif
                            @if($thietLapWebsite->Youtube && $thietLapWebsite->Youtube !== '#')
                                <a href="{{ $thietLapWebsite->Youtube }}" class="icon youtube"
                                    target="_blank" rel="noopener noreferrer" aria-label="YouTube">
                                    <i class="fab fa-youtube" aria-hidden="true"></i>
                                </a>
                            @endif
                            @if($thietLapWebsite->Email)
                                <a href="mailto:{{ $thietLapWebsite->Email }}" class="icon email"
                                    aria-label="Email">
                                    <i class="fas fa-envelope" aria-hidden="true"></i>
                                </a>
                            @endif
                            @if($thietLapWebsite->SoDienThoai)
                                <a href="tel:{{ str_replace(['(', ')', ' ', '-'], '', $thietLapWebsite->SoDienThoai) }}"
                                    class="icon phone" aria-label="Phone">
                                    <i class="fas fa-phone" aria-hidden="true"></i>
                                </a>
                            @endif
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    </div>

    <!-- Edit Sidebar -->
    <div id="editSidebar" class="edit-sidebar">
        <div class="edit-sidebar-content">
            <div class="edit-sidebar-header">
                <h3>Chỉnh sửa thông tin công ty</h3>
                <button class="close-btn" onclick="closeSidebar()">&times;</button>
            </div>
            <div class="edit-sidebar-body">
                <!-- Content will be loaded by JavaScript -->
            </div>
        </div>
    </div>

    <script src="{{ asset('js/congthongtin/edit-sidebar.js') }}"></script>
    <script>
        let editMode = false;
        let editSidebar = null;

        function toggleEditMode() {
            editMode = !editMode;
            document.body.classList.toggle('edit-mode', editMode);
            
            if (editMode && !editSidebar) {
                editSidebar = new EditSidebar();
            }
            
            console.log('Edit mode:', editMode ? 'ON' : 'OFF');
        }

        function closeSidebar() {
            if (editSidebar) {
                editSidebar.hide();
            }
        }

        function testSave() {
            console.log('🧪 Testing save functionality...');
            if (editSidebar) {
                // Simulate form data
                const testData = {
                    companyName: 'TEST COMPANY NAME',
                    softwareName: 'TEST SOFTWARE NAME',
                    facebookUrl: 'https://facebook.com/test',
                    youtubeUrl: 'https://youtube.com/test'
                };
                
                editSidebar.saveCompanyData(testData).then(result => {
                    console.log('Test save result:', result);
                    if (result) {
                        alert('✅ Test save successful! Check console for details.');
                    } else {
                        alert('❌ Test save failed! Check console for errors.');
                    }
                });
            } else {
                alert('Please enable edit mode first!');
            }
        }

        // Auto-enable edit mode for testing
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                toggleEditMode();
                console.log('🚀 Test page loaded. Edit mode enabled automatically.');
            }, 500);
        });
    </script>
</body>
</html>
